{"name": "lasen-soft", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.5.4", "typescript": "5.8.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "dependencies": {"critters": "^0.0.25", "import-in-the-middle": "^1.14.2", "require-in-the-middle": "^7.5.2"}}