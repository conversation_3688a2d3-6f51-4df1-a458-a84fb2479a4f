{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "verify-env": "tsx scripts/verify-env.ts", "analyze": "ANALYZE=true next build", "analyze:server": "BUNDLE_ANALYZE=server next build", "analyze:browser": "BUNDLE_ANALYZE=browser next build"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@repo/ui": "workspace:*", "@sentry/browser": "^9.29.0", "@sentry/nextjs": "^9.29.0", "@sentry/replay": "^7.116.0", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.80.7", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.515.0", "next": "^15.3.0", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "web-vitals": "^3.5.2", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.0", "@playwright/test": "^1.53.0", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "autoprefixer": "^10.4.17", "eslint": "^9.28.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "playwright": "^1.53.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "5.8.2"}}