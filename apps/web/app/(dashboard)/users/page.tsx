import { Suspense } from "react";
import { UsersList } from "@/components/features/users/users-list";
import { UsersHeader } from "@/components/features/users/users-header";

export default function UsersPage() {
  return (
    <div className="container mx-auto py-6">
      <UsersHeader />
      <Suspense fallback={<div>Loading users...</div>}>
        <UsersList />
      </Suspense>
    </div>
  );
} 