import { NextResponse } from "next/server"
import { z } from "zod"

// Mock database (same as in users/route.ts)
let users = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "admin" as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

// Validation schema
const updateUserSchema = z.object({
  name: z.string().min(2).optional(),
  email: z.string().email().optional(),
  role: z.enum(["admin", "user"]).optional(),
})

// GET /api/users/[id]
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const user = users.find((u) => u.id === params.id)
  if (!user) {
    return NextResponse.json(
      { error: "User not found" },
      { status: 404 }
    )
  }
  return NextResponse.json(user)
}

// PATCH /api/users/[id]
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = users.find((u) => u.id === params.id)
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    const body = await request.json()
    const data = updateUserSchema.parse(body)

    const updatedUser = {
      ...user,
      ...data,
      updatedAt: new Date().toISOString(),
    }

    users = users.map((u) => (u.id === params.id ? updatedUser : u))

    return NextResponse.json(updatedUser)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input", details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/users/[id]
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  const user = users.find((u) => u.id === params.id)
  if (!user) {
    return NextResponse.json(
      { error: "User not found" },
      { status: 404 }
    )
  }

  users = users.filter((u) => u.id !== params.id)
  return NextResponse.json({ success: true })
} 