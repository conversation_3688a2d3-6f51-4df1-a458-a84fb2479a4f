import { NextResponse } from "next/server"
import { z } from "zod"
import type { User } from "@/lib/api"

// Mock database
let users: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "admin" as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "user" as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

// Validation schemas
const createUserSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  password: z.string().min(6),
  role: z.enum(["admin", "user"]).optional(),
})

const updateUserSchema = z.object({
  name: z.string().min(2).optional(),
  email: z.string().email().optional(),
  role: z.enum(["admin", "user"]).optional(),
})

// GET /api/users
export async function GET() {
  return NextResponse.json(users)
}

// POST /api/users
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const data = createUserSchema.parse(body)

    const newUser = {
      id: Math.random().toString(36).substr(2, 9),
      ...data,
      role: data.role || "user",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    users.push(newUser)

    return NextResponse.json(newUser, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input", details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: Request) {
  const body = await request.json()
  const { id, ...updates } = body
  const userIndex = users.findIndex((user) => user.id === id)
  
  if (userIndex === -1) {
    return NextResponse.json({ error: "User not found" }, { status: 404 })
  }

  users[userIndex] = { ...users[userIndex], ...updates }
  return NextResponse.json(users[userIndex])
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url)
  const id = searchParams.get("id")
  
  if (!id) {
    return NextResponse.json({ error: "ID is required" }, { status: 400 })
  }

  users = users.filter((user) => user.id !== id)
  return NextResponse.json({ success: true })
} 