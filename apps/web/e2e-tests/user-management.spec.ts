import { test, expect } from '@playwright/test'

test.describe('User Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/users')
  })

  test('should display users list', async ({ page }) => {
    // Wait for the users table to be visible
    await expect(page.locator('[data-testid="users-table"]')).toBeVisible()
    
    // Check if table has at least one row
    const rowCount = await page.locator('[data-testid="users-table"] tbody tr').count()
    expect(rowCount).toBeGreaterThan(0)
  })

  test('should create a new user', async ({ page }) => {
    // Click add user button
    await page.click('[data-testid="add-user-button"]')
    
    // Fill the form
    await page.fill('[data-testid="user-name-input"]', 'Test User')
    await page.fill('[data-testid="user-email-input"]', '<EMAIL>')
    await page.selectOption('[data-testid="user-role-select"]', 'user')
    
    // Submit the form
    await page.click('[data-testid="submit-user-form"]')
    
    // Verify success message
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    
    // Verify user was added to the table
    await expect(page.locator('text=Test User')).toBeVisible()
    await expect(page.locator('text=<EMAIL>')).toBeVisible()
  })

  test('should search users', async ({ page }) => {
    // Wait for users to load
    await expect(page.locator('[data-testid="users-table"]')).toBeVisible()
    
    // Get initial count
    const initialCount = await page.locator('[data-testid="users-table"] tbody tr').count()
    
    // Search for a specific user
    await page.fill('[data-testid="users-search-input"]', 'john')
    
    // Wait for search results
    await page.waitForTimeout(500) // Wait for debounce
    
    // Verify filtered results
    const filteredCount = await page.locator('[data-testid="users-table"] tbody tr').count()
    expect(filteredCount).toBeLessThanOrEqual(initialCount)
  })

  test('should edit user', async ({ page }) => {
    // Click edit button on first user
    await page.click('[data-testid="users-table"] tbody tr:first-child [data-testid="edit-user-button"]')
    
    // Update user name
    await page.fill('[data-testid="user-name-input"]', 'Updated User')
    
    // Submit the form
    await page.click('[data-testid="submit-user-form"]')
    
    // Verify success message
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    
    // Verify user was updated
    await expect(page.locator('text=Updated User')).toBeVisible()
  })

  test('should delete user', async ({ page }) => {
    // Get initial count
    const initialCount = await page.locator('[data-testid="users-table"] tbody tr').count()
    
    // Click delete button on first user
    await page.click('[data-testid="users-table"] tbody tr:first-child [data-testid="delete-user-button"]')
    
    // Confirm deletion
    await page.click('[data-testid="confirm-delete-button"]')
    
    // Verify success message
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    
    // Verify user count decreased
    const newCount = await page.locator('[data-testid="users-table"] tbody tr').count()
    expect(newCount).toBe(initialCount - 1)
  })
}) 