import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { UsersHeader } from '@/components/features/users/users-header'

describe('UsersHeader', () => {
  it('renders the header with title', () => {
    render(<UsersHeader />)
    
    expect(screen.getByText('Users')).toBeInTheDocument()
  })

  it('renders the add user button', () => {
    render(<UsersHeader />)
    
    const addButton = screen.getByRole('button', { name: /add user/i })
    expect(addButton).toBeInTheDocument()
    expect(addButton).toHaveTextContent('Add User')
  })

  it('renders the plus icon in the add button', () => {
    render(<UsersHeader />)
    
    const addButton = screen.getByRole('button', { name: /add user/i })
    expect(addButton.querySelector('svg')).toBeInTheDocument()
  })

  it('has correct layout classes', () => {
    render(<UsersHeader />)
    
    const header = screen.getByRole('heading', { name: /users/i }).parentElement
    expect(header).toHaveClass('flex', 'items-center', 'justify-between', 'mb-6')
  })

  it('has correct heading styles', () => {
    render(<UsersHeader />)
    
    const heading = screen.getByRole('heading', { name: /users/i })
    expect(heading).toHaveClass('text-2xl', 'font-bold', 'tracking-tight')
  })
}) 