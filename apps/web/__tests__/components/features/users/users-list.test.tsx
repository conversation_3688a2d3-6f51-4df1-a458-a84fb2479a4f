import { render, screen, waitFor } from '@testing-library/react'
import { UsersList } from '@/components/features/users/users-list'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useUsers } from '@/hooks/use-users'

// Mock the useUsers hook
jest.mock('@/hooks/use-users', () => ({
  useUsers: jest.fn(),
}))

const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    status: 'inactive',
  },
]

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('UsersList', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders list of users', async () => {
    ;(useUsers as jest.Mock).mockReturnValue({
      users: {
        data: mockUsers,
        isLoading: false,
        error: null,
      },
    })

    render(<UsersList />, { wrapper: createWrapper() })
    
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
  })

  it('displays user information correctly', () => {
    ;(useUsers as jest.Mock).mockReturnValue({
      users: {
        data: mockUsers,
        isLoading: false,
        error: null,
      },
    })

    render(<UsersList />, { wrapper: createWrapper() })
    
    // Check first user
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('user')).toBeInTheDocument()
    
    // Check second user
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('admin')).toBeInTheDocument()
  })

  it('displays loading state', () => {
    ;(useUsers as jest.Mock).mockReturnValue({
      users: {
        data: null,
        isLoading: true,
        error: null,
      },
    })

    render(<UsersList />, { wrapper: createWrapper() })
    
    expect(screen.getByText(/loading users/i)).toBeInTheDocument()
  })

  it('displays empty state when no users found', () => {
    ;(useUsers as jest.Mock).mockReturnValue({
      users: {
        data: [],
        isLoading: false,
        error: null,
      },
    })

    render(<UsersList />, { wrapper: createWrapper() })
    
    expect(screen.getByText(/no users found/i)).toBeInTheDocument()
  })

  it('handles error state', () => {
    ;(useUsers as jest.Mock).mockReturnValue({
      users: {
        data: null,
        isLoading: false,
        error: new Error('Failed to fetch'),
      },
    })

    render(<UsersList />, { wrapper: createWrapper() })
    
    expect(screen.getByText(/error loading users/i)).toBeInTheDocument()
  })
}) 