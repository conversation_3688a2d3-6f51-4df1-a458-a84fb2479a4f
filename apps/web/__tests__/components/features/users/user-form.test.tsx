import { render, screen, fireEvent } from '@testing-library/react'
import { UserForm } from '@/components/features/users/user-form'

// Mock Select to avoid Radix pointer event issues
jest.mock('@/components/ui/select', () => ({
  Select: ({ children, onValueChange, defaultValue }: any) => (
    <div data-testid="mock-select" data-value={defaultValue} onChange={onValueChange}>
      {children}
    </div>
  ),
  SelectTrigger: ({ children }: any) => <button>{children}</button>,
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => <div data-value={value}>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}))

const mockUser = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'user',
  status: 'active',
}

describe('UserForm', () => {
  it('renders create form', () => {
    const mockOnSubmit = jest.fn()
    render(<UserForm onSubmit={mockOnSubmit} />)
    
    // Check for form fields
    expect(screen.getByPlaceholderText(/enter name/i)).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/enter email/i)).toBeInTheDocument()
    expect(screen.getByText(/select a role/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create user/i })).toBeInTheDocument()
  })

  it('renders edit form with user data', () => {
    const mockOnSubmit = jest.fn()
    render(<UserForm user={mockUser} onSubmit={mockOnSubmit} />)
    
    // Check for pre-filled values
    expect(screen.getByPlaceholderText(/enter name/i)).toHaveValue(mockUser.name)
    expect(screen.getByPlaceholderText(/enter email/i)).toHaveValue(mockUser.email)
    expect(screen.getByRole('button', { name: /update user/i })).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const mockOnSubmit = jest.fn()
    render(<UserForm onSubmit={mockOnSubmit} />)
    
    // Submit empty form
    fireEvent.click(screen.getByRole('button', { name: /create user/i }))
    
    // Check for validation messages
    expect(await screen.findByText('Name must be at least 2 characters')).toBeInTheDocument()
    expect(await screen.findByText('Invalid email address')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    const mockOnSubmit = jest.fn()
    render(<UserForm onSubmit={mockOnSubmit} isLoading={true} />)
    expect(screen.getByRole('button', { name: /saving/i })).toBeDisabled()
  })
}) 