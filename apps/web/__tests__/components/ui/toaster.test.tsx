import { render, screen } from '@testing-library/react'
import { Toaster } from '@/components/ui/toaster'
import { useToast } from '@/hooks/use-toast'

// Mock the useToast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: jest.fn(),
}))

describe('Toaster', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders toasts with title and description', () => {
    const mockToasts = [
      {
        id: '1',
        title: 'Test Title',
        description: 'Test Description',
      },
    ]
    ;(useToast as jest.Mock).mockReturnValue({ toasts: mockToasts })

    render(<Toaster />)
    
    expect(screen.getByText('Test Title')).toBeInTheDocument()
    expect(screen.getByText('Test Description')).toBeInTheDocument()
  })

  it('renders multiple toasts', () => {
    const mockToasts = [
      {
        id: '1',
        title: 'First Toast',
        description: 'First Description',
      },
      {
        id: '2',
        title: 'Second Toast',
        description: 'Second Description',
      },
    ]
    ;(useToast as jest.Mock).mockReturnValue({ toasts: mockToasts })

    render(<Toaster />)
    
    expect(screen.getByText('First Toast')).toBeInTheDocument()
    expect(screen.getByText('First Description')).toBeInTheDocument()
    expect(screen.getByText('Second Toast')).toBeInTheDocument()
    expect(screen.getByText('Second Description')).toBeInTheDocument()
  })

  it('renders toast with action', () => {
    const mockToasts = [
      {
        id: '1',
        title: 'Test Title',
        description: 'Test Description',
        action: <button>Action</button>,
      },
    ]
    ;(useToast as jest.Mock).mockReturnValue({ toasts: mockToasts })

    render(<Toaster />)
    
    expect(screen.getByRole('button', { name: 'Action' })).toBeInTheDocument()
  })

  it('renders toast without title or description', () => {
    const mockToasts = [
      {
        id: '1',
      },
    ]
    ;(useToast as jest.Mock).mockReturnValue({ toasts: mockToasts })

    render(<Toaster />)
    
    // Should still render the toast container
    const statuses = screen.getAllByRole('status')
    expect(statuses.length).toBeGreaterThan(0)
  })

  it('renders toast with custom props', () => {
    const mockToasts = [
      {
        id: '1',
        title: 'Test Title',
        description: 'Test Description',
        className: 'custom-toast',
        'data-testid': 'test-toast',
      },
    ]
    ;(useToast as jest.Mock).mockReturnValue({ toasts: mockToasts })

    render(<Toaster />)
    
    const toast = screen.getByTestId('test-toast')
    expect(toast).toHaveClass('custom-toast')
  })
}) 