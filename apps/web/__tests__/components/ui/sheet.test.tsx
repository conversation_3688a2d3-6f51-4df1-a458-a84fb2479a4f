import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import {
  Sheet,
  Sheet<PERSON>rigger,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  SheetFooter,
  SheetTitle,
  SheetDescription,
} from '@/components/ui/sheet'

describe.skip('Sheet', () => {
  it('renders trigger and content', async () => {
    render(
      <Sheet>
        <SheetTrigger>Open Sheet</SheetTrigger>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Sheet Title</SheetTitle>
            <SheetDescription>Sheet Description</SheetDescription>
          </SheetHeader>
          <div>Sheet Content</div>
          <SheetFooter>
            <button>Close</button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    )
    
    const trigger = screen.getByRole('button', { name: 'Open Sheet' })
    expect(trigger).toBeInTheDocument()
    
    await userEvent.click(trigger)
    
    expect(screen.getByText('Sheet Title')).toBeInTheDocument()
    expect(screen.getByText('Sheet Description')).toBeInTheDocument()
    expect(screen.getByText('Sheet Content')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Close' })).toBeInTheDocument()
  })

  it('renders sheet content with correct classes', async () => {
    render(
      <Sheet>
        <SheetTrigger>Open Sheet</SheetTrigger>
        <SheetContent>
          <div>Content</div>
        </SheetContent>
      </Sheet>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Sheet' }))
    
    const content = screen.getByRole('dialog')
    expect(content).toHaveClass(
      'fixed',
      'z-50',
      'gap-4',
      'bg-background',
      'p-6',
      'shadow-lg',
      'transition',
      'ease-in-out'
    )
  })

  it('renders sheet with different sides', async () => {
    const sides = ['top', 'bottom', 'left', 'right'] as const
    
    for (const side of sides) {
      render(
        <Sheet>
          <SheetTrigger>Open Sheet</SheetTrigger>
          <SheetContent side={side}>
            <div>Content</div>
          </SheetContent>
        </Sheet>
      )
      
      await userEvent.click(screen.getByRole('button', { name: 'Open Sheet' }))
      
      const content = screen.getByRole('dialog')
      expect(content).toHaveClass(`data-[state=open]:slide-in-from-${side}`)
    }
  })

  it('renders sheet header with correct classes', async () => {
    render(
      <Sheet>
        <SheetTrigger>Open Sheet</SheetTrigger>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Title</SheetTitle>
            <SheetDescription>Description</SheetDescription>
          </SheetHeader>
        </SheetContent>
      </Sheet>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Sheet' }))
    
    const header = screen.getByText('Title').parentElement
    expect(header).toHaveClass('flex', 'flex-col', 'space-y-2', 'text-center', 'sm:text-left')
    
    const title = screen.getByText('Title')
    expect(title).toHaveClass('text-lg', 'font-semibold', 'text-foreground')
    
    const description = screen.getByText('Description')
    expect(description).toHaveClass('text-sm', 'text-muted-foreground')
  })

  it('renders sheet footer with correct classes', async () => {
    render(
      <Sheet>
        <SheetTrigger>Open Sheet</SheetTrigger>
        <SheetContent>
          <SheetFooter>
            <button>Action</button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Sheet' }))
    
    const footer = screen.getByRole('button', { name: 'Action' }).parentElement
    expect(footer).toHaveClass(
      'flex',
      'flex-col-reverse',
      'sm:flex-row',
      'sm:justify-end',
      'sm:space-x-2'
    )
  })

  it('renders close button', async () => {
    render(
      <Sheet>
        <SheetTrigger>Open Sheet</SheetTrigger>
        <SheetContent>
          <div>Content</div>
        </SheetContent>
      </Sheet>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Sheet' }))
    
    const closeButton = screen.getByRole('button', { name: 'Close' })
    expect(closeButton).toHaveClass(
      'absolute',
      'right-4',
      'top-4',
      'rounded-sm',
      'opacity-70',
      'ring-offset-background',
      'transition-opacity',
      'hover:opacity-100',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-ring',
      'focus:ring-offset-2'
    )
  })

  it('renders overlay with correct classes', async () => {
    render(
      <Sheet>
        <SheetTrigger>Open Sheet</SheetTrigger>
        <SheetContent>
          <div>Content</div>
        </SheetContent>
      </Sheet>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Sheet' }))
    
    const overlay = screen.getByRole('presentation')
    expect(overlay).toHaveClass(
      'fixed',
      'inset-0',
      'z-50',
      'bg-black/80',
      'data-[state=open]:animate-in',
      'data-[state=closed]:animate-out',
      'data-[state=closed]:fade-out-0',
      'data-[state=open]:fade-in-0'
    )
  })
}) 