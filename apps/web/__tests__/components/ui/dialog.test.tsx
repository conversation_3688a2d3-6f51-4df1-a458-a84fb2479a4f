import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog'

describe('Dialog', () => {
  it('renders trigger button', () => {
    render(
      <Dialog>
        <DialogTrigger>Open Dialog</DialogTrigger>
      </Dialog>
    )
    expect(screen.getByRole('button', { name: /open dialog/i })).toBeInTheDocument()
  })

  it('opens dialog when trigger is clicked', async () => {
    render(
      <Dialog>
        <DialogTrigger>Open Dialog</DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Dialog Title</DialogTitle>
            <DialogDescription>Dialog Description</DialogDescription>
          </DialogHeader>
          <div>Dialog Content</div>
          <DialogFooter>
            <DialogClose>Close</DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
    const trigger = screen.getByRole('button', { name: /open dialog/i })
    await userEvent.click(trigger)
    expect(screen.getByText('Dialog Title')).toBeInTheDocument()
    expect(screen.getByText('Dialog Description')).toBeInTheDocument()
    expect(screen.getByText('Dialog Content')).toBeInTheDocument()
    // Use getAllByRole to avoid ambiguity
    const closeButtons = screen.getAllByRole('button', { name: /close/i })
    expect(closeButtons.length).toBeGreaterThan(0)
  })

  it('closes dialog when close button is clicked', async () => {
    render(
      <Dialog>
        <DialogTrigger>Open Dialog</DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Dialog Title</DialogTitle>
            <DialogDescription>Dialog Description</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose>Close</DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
    // Open dialog
    const trigger = screen.getByRole('button', { name: /open dialog/i })
    await userEvent.click(trigger)
    // Use getAllByRole to avoid ambiguity
    const closeButtons = screen.getAllByRole('button', { name: /close/i })
    await userEvent.click(closeButtons[0])
    // Verify dialog is closed
    expect(screen.queryByText('Dialog Title')).not.toBeInTheDocument()
  })

  it('closes dialog when clicking outside', async () => {
    render(
      <Dialog>
        <DialogTrigger>Open Dialog</DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Dialog Title</DialogTitle>
            <DialogDescription>Dialog Description</DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    )
    // Open dialog
    const trigger = screen.getByRole('button', { name: /open dialog/i })
    await userEvent.click(trigger)
    
    // Press Escape key instead of clicking outside since it's more reliable in jsdom
    await userEvent.keyboard('{Escape}')
    
    // Verify dialog is closed
    expect(screen.queryByText('Dialog Title')).not.toBeInTheDocument()
  })

  it('applies custom className', async () => {
    render(
      <Dialog>
        <DialogTrigger>Open Dialog</DialogTrigger>
        <DialogContent className="custom-class">
          <DialogHeader>
            <DialogTitle>Dialog Title</DialogTitle>
            <DialogDescription>Dialog Description</DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    )
    const trigger = screen.getByRole('button', { name: /open dialog/i })
    await userEvent.click(trigger)
    const content = screen.getByRole('dialog')
    expect(content).toHaveClass('custom-class')
  })

  it('handles keyboard navigation', async () => {
    render(
      <Dialog>
        <DialogTrigger>Open Dialog</DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Dialog Title</DialogTitle>
            <DialogDescription>Dialog Description</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose>Close</DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
    // Open dialog
    const trigger = screen.getByRole('button', { name: /open dialog/i })
    await userEvent.click(trigger)
    // Press Escape key
    await userEvent.keyboard('{Escape}')
    // Verify dialog is closed
    expect(screen.queryByText('Dialog Title')).not.toBeInTheDocument()
  })
}) 