import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
} from '@/components/ui/dropdown-menu'

describe('DropdownMenu', () => {
  it('renders trigger and content', async () => {
    render(
      <DropdownMenu>
        <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem>Item 1</DropdownMenuItem>
          <DropdownMenuItem>Item 2</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
    
    const trigger = screen.getByRole('button', { name: 'Open Menu' })
    expect(trigger).toBeInTheDocument()
    
    await userEvent.click(trigger)
    
    expect(screen.getByText('Item 1')).toBeInTheDocument()
    expect(screen.getByText('Item 2')).toBeInTheDocument()
  })

  it('renders menu items with correct classes', async () => {
    render(
      <DropdownMenu>
        <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem>Item 1</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Menu' }))
    
    const item = screen.getByText('Item 1')
    expect(item).toHaveClass(
      'relative',
      'flex',
      'cursor-default',
      'select-none',
      'items-center',
      'gap-2',
      'rounded-sm',
      'px-2',
      'py-1.5',
      'text-sm',
      'outline-none',
      'transition-colors'
    )
  })

  it('renders checkbox items', async () => {
    render(
      <DropdownMenu>
        <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuCheckboxItem checked>Checkbox Item</DropdownMenuCheckboxItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Menu' }))
    
    const item = screen.getByText('Checkbox Item')
    expect(item).toHaveClass(
      'relative',
      'flex',
      'cursor-default',
      'select-none',
      'items-center',
      'rounded-sm',
      'py-1.5',
      'pl-8',
      'pr-2',
      'text-sm',
      'outline-none',
      'transition-colors'
    )
  })

  it('renders radio items', async () => {
    render(
      <DropdownMenu>
        <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuRadioGroup value="option1">
            <DropdownMenuRadioItem value="option1">Radio Item</DropdownMenuRadioItem>
          </DropdownMenuRadioGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Menu' }))
    
    const item = screen.getByText('Radio Item')
    expect(item).toHaveClass(
      'relative',
      'flex',
      'cursor-default',
      'select-none',
      'items-center',
      'rounded-sm',
      'py-1.5',
      'pl-8',
      'pr-2',
      'text-sm',
      'outline-none',
      'transition-colors'
    )
  })

  it('renders submenu', async () => {
    render(
      <DropdownMenu>
        <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>Submenu</DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuItem>Submenu Item</DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuSub>
        </DropdownMenuContent>
      </DropdownMenu>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Menu' }))
    await userEvent.click(screen.getByText('Submenu'))
    
    expect(screen.getByText('Submenu Item')).toBeInTheDocument()
  })

  it('renders menu with label and separator', async () => {
    render(
      <DropdownMenu>
        <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Label</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem>Item</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Menu' }))
    
    expect(screen.getByText('Label')).toHaveClass('px-2', 'py-1.5', 'text-sm', 'font-semibold')
    expect(screen.getByRole('separator')).toHaveClass('-mx-1', 'my-1', 'h-px', 'bg-muted')
  })

  it('renders menu with shortcut', async () => {
    render(
      <DropdownMenu>
        <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem>
            Item
            <DropdownMenuShortcut>⌘K</DropdownMenuShortcut>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Menu' }))
    
    const shortcut = screen.getByText('⌘K')
    expect(shortcut).toHaveClass('ml-auto', 'text-xs', 'tracking-widest', 'opacity-60')
  })

  it('handles disabled items', async () => {
    render(
      <DropdownMenu>
        <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem disabled>Disabled Item</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
    
    await userEvent.click(screen.getByRole('button', { name: 'Open Menu' }))
    
    const item = screen.getByText('Disabled Item')
    expect(item).toHaveClass('data-[disabled]:pointer-events-none', 'data-[disabled]:opacity-50')
  })
}) 