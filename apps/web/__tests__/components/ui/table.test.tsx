import { render, screen } from '@testing-library/react'
import {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
} from '@/components/ui/table'

describe('Table', () => {
  const mockData = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>' },
  ]

  it('renders with basic structure', () => {
    render(
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {mockData.map((user) => (
            <TableRow key={user.id}>
              <TableCell>{user.name}</TableCell>
              <TableCell>{user.email}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    )

    expect(screen.getByText('Name')).toBeInTheDocument()
    expect(screen.getByText('Email')).toBeInTheDocument()
    expect(screen.getByText('<PERSON>')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('renders with caption', () => {
    render(
      <Table>
        <TableCaption>User List</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>John Doe</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    )

    expect(screen.getByText('User List')).toBeInTheDocument()
  })

  it('renders with footer', () => {
    render(
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>John Doe</TableCell>
          </TableRow>
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell>Total: 1</TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    )

    expect(screen.getByText('Total: 1')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(
      <Table className="custom-class">
        <TableBody>
          <TableRow>
            <TableCell>Content</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    )

    const table = screen.getByText('Content').closest('table')
    expect(table).toHaveClass('custom-class')
  })

  it('maintains proper styling for different elements', () => {
    render(
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Header</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>Cell</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    )

    const header = screen.getByText('Header')
    const cell = screen.getByText('Cell')

    expect(header).toHaveClass('h-12 px-4 text-left align-middle font-medium')
    expect(cell).toHaveClass('p-4 align-middle')
  })

  it('handles empty table', () => {
    render(
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>No data available</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    )

    expect(screen.getByText('No data available')).toBeInTheDocument()
  })

  it('renders table with correct structure', () => {
    render(
      <Table>
        <TableCaption>Table Caption</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Header 1</TableHead>
            <TableHead>Header 2</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>Cell 1</TableCell>
            <TableCell>Cell 2</TableCell>
          </TableRow>
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell>Footer 1</TableCell>
            <TableCell>Footer 2</TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    )
    
    expect(screen.getByText('Table Caption')).toBeInTheDocument()
    expect(screen.getByText('Header 1')).toBeInTheDocument()
    expect(screen.getByText('Header 2')).toBeInTheDocument()
    expect(screen.getByText('Cell 1')).toBeInTheDocument()
    expect(screen.getByText('Cell 2')).toBeInTheDocument()
    expect(screen.getByText('Footer 1')).toBeInTheDocument()
    expect(screen.getByText('Footer 2')).toBeInTheDocument()
  })

  it('renders table with correct classes', () => {
    render(
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Header</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>Cell</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    )
    
    const table = screen.getByRole('table')
    expect(table).toHaveClass('w-full', 'caption-bottom', 'text-sm')
    
    const header = screen.getByText('Header')
    expect(header).toHaveClass(
      'h-12',
      'px-4',
      'text-left',
      'align-middle',
      'font-medium',
      'text-muted-foreground'
    )
    
    const cell = screen.getByText('Cell')
    expect(cell).toHaveClass('p-4', 'align-middle')
  })

  it('renders table with custom class names', () => {
    render(
      <Table className="custom-table">
        <TableHeader className="custom-header">
          <TableRow className="custom-row">
            <TableHead className="custom-head">Header</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="custom-body">
          <TableRow className="custom-row">
            <TableCell className="custom-cell">Cell</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    )
    
    const table = screen.getByRole('table')
    expect(table).toHaveClass('custom-table')
    
    const header = screen.getByText('Header')
    expect(header).toHaveClass('custom-head')
    
    const cell = screen.getByText('Cell')
    expect(cell).toHaveClass('custom-cell')
  })

  it('renders table with hover and selected states', () => {
    render(
      <Table>
        <TableBody>
          <TableRow data-state="selected">
            <TableCell>Selected Cell</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Hover Cell</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    )
    
    const selectedRow = screen.getByText('Selected Cell').parentElement
    expect(selectedRow).toHaveClass('data-[state=selected]:bg-muted')
    
    const hoverRow = screen.getByText('Hover Cell').parentElement
    expect(hoverRow).toHaveClass('hover:bg-muted/50')
  })

  it('renders table with checkbox cells', () => {
    render(
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              <input type="checkbox" role="checkbox" />
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>
              <input type="checkbox" role="checkbox" />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    )
    
    const header = screen.getByRole('columnheader')
    expect(header).toHaveClass('[&:has([role=checkbox])]:pr-0')
    
    const cell = screen.getByRole('cell')
    expect(cell).toHaveClass('[&:has([role=checkbox])]:pr-0')
  })

  it('renders table with caption', () => {
    render(
      <Table>
        <TableCaption>Table Description</TableCaption>
        <TableBody>
          <TableRow>
            <TableCell>Cell</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    )
    
    const caption = screen.getByText('Table Description')
    expect(caption).toHaveClass('mt-4', 'text-sm', 'text-muted-foreground')
  })

  it('renders table with footer', () => {
    render(
      <Table>
        <TableBody>
          <TableRow>
            <TableCell>Cell</TableCell>
          </TableRow>
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell>Footer</TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    )
    
    const footer = screen.getByText('Footer').parentElement?.parentElement
    expect(footer).toHaveClass('border-t', 'bg-muted/50', 'font-medium')
  })
}) 