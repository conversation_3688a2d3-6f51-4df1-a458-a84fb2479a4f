import { render, screen } from '@testing-library/react'
import { Skeleton } from '@/components/ui/skeleton'

describe('Skeleton', () => {
  it('renders with correct base classes', () => {
    render(<Skeleton data-testid="skeleton" />)
    
    const skeleton = screen.getByTestId('skeleton')
    expect(skeleton).toHaveClass('animate-pulse', 'rounded-md', 'bg-muted')
  })

  it('applies custom class names', () => {
    render(<Skeleton className="custom-class" data-testid="skeleton" />)
    
    const skeleton = screen.getByTestId('skeleton')
    expect(skeleton).toHaveClass('custom-class')
  })

  it('forwards additional props', () => {
    render(<Skeleton data-testid="test" aria-label="Loading" />)
    
    const skeleton = screen.getByTestId('test')
    expect(skeleton).toHaveAttribute('aria-label', 'Loading')
  })

  it('renders with custom dimensions', () => {
    render(<Skeleton className="h-10 w-20" data-testid="skeleton" />)
    
    const skeleton = screen.getByTestId('skeleton')
    expect(skeleton).toHaveClass('h-10', 'w-20')
  })
}) 