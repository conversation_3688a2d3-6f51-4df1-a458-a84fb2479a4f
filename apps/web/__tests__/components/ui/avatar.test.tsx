import { render, screen, fireEvent } from '@testing-library/react'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'

describe('Avatar', () => {
  it('renders the avatar root with correct classes', () => {
    render(<Avatar data-testid="avatar-root" />)
    const avatar = screen.getByTestId('avatar-root')
    expect(avatar).toHaveClass(
      'relative',
      'flex',
      'h-10',
      'w-10',
      'shrink-0',
      'overflow-hidden',
      'rounded-full'
    )
  })

  it('renders the avatar fallback by default in JSDOM', () => {
    render(
      <Avatar>
        <AvatarImage src="/test.jpg" alt="Test" />
        <AvatarFallback data-testid="avatar-fallback">JD</AvatarFallback>
      </Avatar>
    )
    // In JSDOM, the image never loads, so fallback is always rendered
    expect(screen.getByTestId('avatar-fallback')).toBeInTheDocument()
    // The image should not be in the document
    expect(screen.queryByAltText('Test')).not.toBeInTheDocument()
  })

  it('renders the avatar fallback with correct classes', () => {
    render(
      <Avatar>
        <AvatarFallback data-testid="avatar-fallback">JD</AvatarFallback>
      </Avatar>
    )
    const fallback = screen.getByTestId('avatar-fallback')
    expect(fallback).toHaveClass(
      'flex',
      'h-full',
      'w-full',
      'items-center',
      'justify-center',
      'rounded-full',
      'bg-muted'
    )
  })

  it('applies custom class names', () => {
    render(
      <Avatar data-testid="avatar-root" className="custom-class">
        <AvatarImage src="/test.jpg" alt="Test" />
        <AvatarFallback data-testid="avatar-fallback" className="fallback-class">JD</AvatarFallback>
      </Avatar>
    )
    const avatar = screen.getByTestId('avatar-root')
    expect(avatar).toHaveClass('custom-class')
    const fallback = screen.getByTestId('avatar-fallback')
    expect(fallback).toHaveClass('fallback-class')
    // The image should not be in the document
    expect(screen.queryByAltText('Test')).not.toBeInTheDocument()
  })

  it('shows fallback when image fails to load', () => {
    render(
      <Avatar>
        <AvatarImage src="/invalid.jpg" alt="Test" />
        <AvatarFallback data-testid="avatar-fallback">JD</AvatarFallback>
      </Avatar>
    )
    // In JSDOM, fallback is always rendered
    expect(screen.getByTestId('avatar-fallback')).toBeInTheDocument()
  })
}) 