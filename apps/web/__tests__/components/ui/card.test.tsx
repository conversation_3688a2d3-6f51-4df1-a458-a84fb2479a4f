import { render, screen } from '@testing-library/react'
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card'

describe('Card', () => {
  it('renders with default props', () => {
    render(<Card data-testid="card">Card content</Card>)
    const card = screen.getByTestId('card')
    expect(card).toBeInTheDocument()
    expect(card).toHaveClass('rounded-lg border bg-card')
  })

  it('renders with header', () => {
    render(
      <Card>
        <CardHeader data-testid="header">
          <CardTitle>Card Title</CardTitle>
          <CardDescription>Card Description</CardDescription>
        </CardHeader>
        <CardContent>Card content</CardContent>
      </Card>
    )

    expect(screen.getByText('Card Title')).toBeInTheDocument()
    expect(screen.getByText('Card Description')).toBeInTheDocument()
    expect(screen.getByText('Card content')).toBeInTheDocument()
  })

  it('renders with footer', () => {
    render(
      <Card>
        <CardContent>Card content</CardContent>
        <CardFooter data-testid="footer">Card footer</CardFooter>
      </Card>
    )

    expect(screen.getByText('Card content')).toBeInTheDocument()
    expect(screen.getByText('Card footer')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(<Card className="custom-class" data-testid="card">Card content</Card>)
    const card = screen.getByTestId('card')
    expect(card).toHaveClass('custom-class')
  })

  it('renders with all sections', () => {
    render(
      <Card>
        <CardHeader data-testid="header">
          <CardTitle>Card Title</CardTitle>
          <CardDescription>Card Description</CardDescription>
        </CardHeader>
        <CardContent data-testid="content">Card content</CardContent>
        <CardFooter data-testid="footer">Card footer</CardFooter>
      </Card>
    )

    expect(screen.getByText('Card Title')).toBeInTheDocument()
    expect(screen.getByText('Card Description')).toBeInTheDocument()
    expect(screen.getByText('Card content')).toBeInTheDocument()
    expect(screen.getByText('Card footer')).toBeInTheDocument()
  })

  it('maintains proper spacing between sections', () => {
    render(
      <Card>
        <CardHeader data-testid="header">
          <CardTitle>Card Title</CardTitle>
        </CardHeader>
        <CardContent data-testid="content">Card content</CardContent>
        <CardFooter data-testid="footer">Card footer</CardFooter>
      </Card>
    )

    const header = screen.getByTestId('header')
    const content = screen.getByTestId('content')
    const footer = screen.getByTestId('footer')

    expect(header).toHaveClass('flex flex-col space-y-1.5 p-6')
    expect(content).toHaveClass('p-6 pt-0')
    expect(footer).toHaveClass('flex items-center p-6 pt-0')
  })
}) 