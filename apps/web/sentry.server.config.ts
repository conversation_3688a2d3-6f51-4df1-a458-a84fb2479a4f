import * as Sentry from '@sentry/nextjs'
import { env } from './lib/env'

Sentry.init({
  dsn: env.NEXT_PUBLIC_SENTRY_DSN,
  environment: env.NODE_ENV,
  
  // Performance monitoring
  tracesSampleRate: env.NODE_ENV === 'production' ? 0.2 : 1.0,
  profilesSampleRate: env.NODE_ENV === 'production' ? 0.1 : 1.0,
  
  // Debug mode only in development
  debug: env.NODE_ENV === 'development',
  
  // Ignore specific errors
  ignoreErrors: [
    // Ignore specific error messages
    'Network request failed',
    'Failed to fetch',
    // Ignore specific error types
    'ChunkLoadError',
    'TypeError: Failed to fetch',
  ],
  
  // Custom tags
  initialScope: {
    tags: {
      app: 'web',
      environment: env.NODE_ENV,
    },
  },
}) 