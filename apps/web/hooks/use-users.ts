import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { apiService, CreateUserInput, UpdateUserInput, User } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import axios from "axios"

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

async function getUsers(): Promise<User[]> {
  const { data } = await axios.get("/api/users");
  return data;
}

export function useUsers() {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const users = useQuery<User[]>({
    queryKey: ["users"],
    queryFn: getUsers,
  })

  const createUser = useMutation({
    mutationFn: apiService.users.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] })
      toast({
        title: "Success",
        description: "User created successfully",
      })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create user",
        variant: "destructive",
      })
    },
  })

  const updateUser = useMutation({
    mutationFn: ({ id, input }: { id: string; input: UpdateUserInput }) =>
      apiService.users.update(id, input),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] })
      toast({
        title: "Success",
        description: "User updated successfully",
      })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update user",
        variant: "destructive",
      })
    },
  })

  const deleteUser = useMutation({
    mutationFn: apiService.users.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] })
      toast({
        title: "Success",
        description: "User deleted successfully",
      })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete user",
        variant: "destructive",
      })
    },
  })

  return {
    users,
    createUser,
    updateUser,
    deleteUser,
  }
} 