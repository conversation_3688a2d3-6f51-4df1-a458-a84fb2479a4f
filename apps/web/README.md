This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/create-next-app).

# Web Application

## Environment Setup

1. Copy the environment template:
```bash
cp env.template .env.local
```

2. Update the values in `.env.local` with your actual configuration:
- Replace placeholder values with your actual credentials
- Update URLs to match your development environment
- Set appropriate feature flags

3. Required environment variables:
- `NEXT_PUBLIC_APP_URL`: Your application URL
- `NEXT_PUBLIC_API_URL`: Your API URL
- `DATABASE_URL`: Your database connection string
- `NEXTAUTH_SECRET`: A secure random string for NextAuth.js
- `NEXTAUTH_URL`: Your application URL (same as NEXT_PUBLIC_APP_URL)

4. Optional environment variables:
- OAuth provider credentials (Google, GitHub)
- External service keys (Stripe, SendGrid)
- Redis configuration
- AWS credentials
- Monitoring tools (Sentry, Vercel Analytics)

## Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Run tests
pnpm test

# Run type checking
pnpm type-check

# Run linting
pnpm lint
```

## Deployment

The application is configured for deployment on Vercel with GitHub Actions CI/CD.

1. Set up Vercel:
```bash
# Install Vercel CLI
pnpm add -g vercel

# Login to Vercel
vercel login

# Link project
vercel link
```

2. Set up environment variables in Vercel dashboard:
- Copy all required variables from `.env.local`
- Add production-specific values
- Set up secrets for external services

3. GitHub Actions secrets required:
- `VERCEL_TOKEN`
- `VERCEL_ORG_ID`
- `VERCEL_PROJECT_ID`
- `TURBO_TOKEN`
- `TURBO_TEAM`

## Environment Files

- `.env.local`: Local development (gitignored)
- `.env.test`: Test environment (gitignored)
- `.env.staging`: Staging environment (gitignored)
- `.env.production`: Production environment (gitignored)

Copy `env.template` to create any of these files and update the values accordingly.

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
