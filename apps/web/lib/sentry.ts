import * as Sentry from '@sentry/nextjs'
import { browserTracingIntegration } from '@sentry/browser'
import { Replay } from '@sentry/replay'
import { env } from './env'

// Initialize Sentry with best practices configuration
export function initSentry() {
  if (!env.NEXT_PUBLIC_SENTRY_DSN) {
    console.warn('Sentry DSN not found. Error tracking is disabled.')
    return
  }

  Sentry.init({
    dsn: env.NEXT_PUBLIC_SENTRY_DSN,
    environment: env.NODE_ENV,
    
    // Performance monitoring
    tracesSampleRate: env.NODE_ENV === 'production' ? 0.2 : 1.0,
    profilesSampleRate: env.NODE_ENV === 'production' ? 0.1 : 1.0,
    
    // Session replay
    replaysSessionSampleRate: env.NODE_ENV === 'production' ? 0.1 : 1.0,
    replaysOnErrorSampleRate: 1.0,
    
    // Debug mode only in development
    debug: env.NODE_ENV === 'development',

    // Trace propagation targets (top-level, not in integration)
    tracePropagationTargets: [
      'localhost',
      /^https:\/\/.*\.yourdomain\.com/,
      /^https:\/\/api\.yourdomain\.com/,
    ],
    
    // Integrations
    integrations: [
      browserTracingIntegration(),
      new Replay({
        maskAllText: true,
        blockAllMedia: true,
      }),
    ],
    
    // Ignore specific errors
    ignoreErrors: [
      // Ignore specific error messages
      'Network request failed',
      'Failed to fetch',
      // Ignore specific error types
      'ChunkLoadError',
      'TypeError: Failed to fetch',
    ],
    
    // Custom tags
    initialScope: {
      tags: {
        app: 'web',
        environment: env.NODE_ENV,
      },
    },
  })
}

// Helper function to capture exceptions
export function captureException(error: Error, context?: Record<string, any>) {
  if (env.NODE_ENV === 'development') {
    console.error('Error:', error)
    console.error('Context:', context)
  }
  
  Sentry.captureException(error, {
    extra: context,
  })
}

// Helper function to capture messages
export function captureMessage(message: string, level: Sentry.SeverityLevel = 'info') {
  if (env.NODE_ENV === 'development') {
    console.log(`[${level.toUpperCase()}] ${message}`)
  }
  
  Sentry.captureMessage(message, {
    level,
  })
}

// Helper function to set user context
export function setUserContext(user: {  email?: string; username?: string }) {
  Sentry.setUser(user)
}

// Helper function to clear user context
export function clearUserContext() {
  Sentry.setUser(null)
}

// Helper function to add breadcrumb
export function addBreadcrumb(breadcrumb: Sentry.Breadcrumb) {
  Sentry.addBreadcrumb(breadcrumb)
} 