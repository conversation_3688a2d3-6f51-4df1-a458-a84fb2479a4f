import { env } from './env'

export interface FeatureFlags {
  enableAnalytics: boolean
  enableMaintenanceMode: boolean
  enableNewDashboard: boolean
  enableBetaFeatures: boolean
}

export function getFeatureFlags(): FeatureFlags {
  return {
    enableAnalytics: env.NEXT_PUBLIC_ENABLE_ANALYTICS,
    enableMaintenanceMode: env.NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE,
    enableNewDashboard: env.NODE_ENV !== 'production', // Beta in non-prod
    enableBetaFeatures: env.NODE_ENV === 'development',
  }
}

// Hook for using feature flags
export function useFeatureFlags() {
  return getFeatureFlags()
} 