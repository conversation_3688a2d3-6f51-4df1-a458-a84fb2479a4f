import { captureException, addBreadcrumb } from './sentry'

interface ApiOptions extends RequestInit {
  timeout?: number
}

export async function sentryApi<T>(
  url: string,
  options: ApiOptions = {}
): Promise<T> {
  const { timeout = 30000, ...fetchOptions } = options

  // Add breadcrumb for API call
  addBreadcrumb({
    category: 'api',
    message: `API Call: ${url}`,
    level: 'info',
    data: {
      url,
      method: options.method || 'GET',
    },
  })

  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    const response = await fetch(url, {
      ...fetchOptions,
      signal: controller.signal,
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const error = new Error(`API Error: ${response.status} ${response.statusText}`)
      captureException(error, {
        extra: {
          url,
          status: response.status,
          statusText: response.statusText,
          response: await response.text(),
        },
      })
      throw error
    }

    return response.json()
  } catch (error) {
    if (error instanceof Error) {
      // Add breadcrumb for API error
      addBreadcrumb({
        category: 'api',
        message: `API Error: ${error.message}`,
        level: 'error',
        data: {
          url,
          error: error.message,
        },
      })

      captureException(error, {
        extra: {
          url,
          options: fetchOptions,
        },
      })
    }
    throw error
  }
}

// Helper function for GET requests
export function sentryGet<T>(url: string, options: ApiOptions = {}) {
  return sentryApi<T>(url, { ...options, method: 'GET' })
}

// Helper function for POST requests
export function sentryPost<T>(url: string, data: unknown, options: ApiOptions = {}) {
  return sentryApi<T>(url, {
    ...options,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    body: JSON.stringify(data),
  })
}

// Helper function for PUT requests
export function sentryPut<T>(url: string, data: unknown, options: ApiOptions = {}) {
  return sentryApi<T>(url, {
    ...options,
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    body: JSON.stringify(data),
  })
}

// Helper function for DELETE requests
export function sentryDelete<T>(url: string, options: ApiOptions = {}) {
  return sentryApi<T>(url, { ...options, method: 'DELETE' })
} 