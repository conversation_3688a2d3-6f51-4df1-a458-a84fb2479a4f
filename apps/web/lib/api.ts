import axios from "axios"
import { captureException, addBreadcrumb } from './sentry'

// Create axios instance with default config
export const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api",
  headers: {
    "Content-Type": "application/json",
  },
})

// Add request interceptor for auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem("token")
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 Unauthorized
    if (error.response?.status === 401) {
      localStorage.removeItem("token")
      window.location.href = "/login"
    }
    return Promise.reject(error)
  }
)

// API endpoints
export const endpoints = {
  users: {
    list: "/users",
    create: "/users",
    update: (id: string) => `/users/${id}`,
    delete: (id: string) => `/users/${id}`,
  },
  auth: {
    login: "/auth/login",
    register: "/auth/register",
    logout: "/auth/logout",
  },
} as const

// Types
export interface User {
  id: string
  name: string
  email: string
  role: string
}

export interface CreateUserInput {
  name: string
  email: string
  role: string
}

export interface UpdateUserInput extends Partial<CreateUserInput> {
  id: string
}

// API functions
export const apiService = {
  users: {
    list: async () => {
      const { data } = await api.get<User[]>(endpoints.users.list)
      return data
    },
    create: async (input: CreateUserInput) => {
      const { data } = await api.post<User>(endpoints.users.create, input)
      return data
    },
    update: async (input: UpdateUserInput) => {
      const { data } = await api.patch<User>(endpoints.users.update(input.id), input)
      return data
    },
    delete: async (id: string) => {
      await api.delete(endpoints.users.delete(id))
    },
  },
  auth: {
    login: async (email: string, password: string) => {
      const { data } = await api.post<{ token: string; user: User }>(
        endpoints.auth.login,
        { email, password }
      )
      return data
    },
    register: async (input: CreateUserInput) => {
      const { data } = await api.post<{ token: string; user: User }>(
        endpoints.auth.register,
        input
      )
      return data
    },
    logout: async () => {
      await api.post(endpoints.auth.logout)
      localStorage.removeItem("token")
    },
  },
}

interface ApiOptions extends RequestInit {
  timeout?: number
}

export async function api<T>(
  url: string,
  options: ApiOptions = {}
): Promise<T> {
  const { timeout = 30000, ...fetchOptions } = options

  // Add breadcrumb for API call
  addBreadcrumb({
    category: 'api',
    message: `API Call: ${url}`,
    level: 'info',
    data: {
      url,
      method: options.method || 'GET',
    },
  })

  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    const response = await fetch(url, {
      ...fetchOptions,
      signal: controller.signal,
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const error = new Error(`API Error: ${response.status} ${response.statusText}`)
      captureException(error, {
        extra: {
          url,
          status: response.status,
          statusText: response.statusText,
          response: await response.text(),
        },
      })
      throw error
    }

    return response.json()
  } catch (error) {
    if (error instanceof Error) {
      // Add breadcrumb for API error
      addBreadcrumb({
        category: 'api',
        message: `API Error: ${error.message}`,
        level: 'error',
        data: {
          url,
          error: error.message,
        },
      })

      captureException(error, {
        extra: {
          url,
          options: fetchOptions,
        },
      })
    }
    throw error
  }
}

// Helper function for GET requests
export function get<T>(url: string, options: ApiOptions = {}) {
  return api<T>(url, { ...options, method: 'GET' })
}

// Helper function for POST requests
export function post<T>(url: string, data: unknown, options: ApiOptions = {}) {
  return api<T>(url, {
    ...options,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    body: JSON.stringify(data),
  })
}

// Helper function for PUT requests
export function put<T>(url: string, data: unknown, options: ApiOptions = {}) {
  return api<T>(url, {
    ...options,
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    body: JSON.stringify(data),
  })
}

// Helper function for DELETE requests
export function del<T>(url: string, options: ApiOptions = {}) {
  return api<T>(url, { ...options, method: 'DELETE' })
} 