'use client'

import { createContext, useContext } from 'react'
import { env } from './env'
import { getFeatureFlags, FeatureFlags } from './feature-flags'

interface AppConfig {
  env: typeof env
  featureFlags: FeatureFlags
  apiUrl: string
  appUrl: string
}

const ConfigContext = createContext<AppConfig | null>(null)

export function ConfigProvider({ children }: { children: React.ReactNode }) {
  const config: AppConfig = {
    env,
    featureFlags: getFeatureFlags(),
    apiUrl: env.NEXT_PUBLIC_API_URL,
    appUrl: env.NEXT_PUBLIC_APP_URL,
  }

  return (
    <ConfigContext.Provider value={config}>
      {children}
    </ConfigContext.Provider>
  )
}

export function useConfig() {
  const config = useContext(ConfigContext)
  if (!config) {
    throw new Error('useConfig must be used within ConfigProvider')
  }
  return config
} 