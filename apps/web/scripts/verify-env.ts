import { env } from '../lib/env'

function verifyEnvironment() {
  console.log('🔍 Verifying environment configuration...\n')

  // Check required variables
  const requiredVars = [
    'NODE_ENV',
    'NEXT_PUBLIC_APP_URL',
    'NEXT_PUBLIC_API_URL',
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL'
  ]

  console.log('Required Variables:')
  requiredVars.forEach(varName => {
    const value = env[varName as keyof typeof env]
    const status = value ? '✅' : '❌'
    console.log(`${status} ${varName}: ${value ? 'Set' : 'Missing'}`)
  })

  // Check URLs
  console.log('\nURL Validation:')
  try {
    new URL(env.NEXT_PUBLIC_APP_URL)
    console.log('✅ NEXT_PUBLIC_APP_URL: Valid URL')
  } catch {
    console.log('❌ NEXT_PUBLIC_APP_URL: Invalid URL')
  }

  try {
    new URL(env.NEXT_PUBLIC_API_URL)
    console.log('✅ NEXT_PUBLIC_API_URL: Valid URL')
  } catch {
    console.log('❌ NEXT_PUBLIC_API_URL: Invalid URL')
  }

  try {
    new URL(env.NEXTAUTH_URL)
    console.log('✅ NEXTAUTH_URL: Valid URL')
  } catch {
    console.log('❌ NEXTAUTH_URL: Invalid URL')
  }

  // Check NEXTAUTH_SECRET length
  console.log('\nSecurity Checks:')
  if (env.NEXTAUTH_SECRET.length >= 32) {
    console.log('✅ NEXTAUTH_SECRET: Sufficient length')
  } else {
    console.log('❌ NEXTAUTH_SECRET: Too short (minimum 32 characters)')
  }

  // Check optional variables
  console.log('\nOptional Variables:')
  const optionalVars = [
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'GITHUB_CLIENT_ID',
    'GITHUB_CLIENT_SECRET',
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'SENDGRID_API_KEY',
    'REDIS_URL',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_S3_BUCKET',
    'NEXT_PUBLIC_SENTRY_DSN',
    'VERCEL_ANALYTICS_ID'
  ]

  optionalVars.forEach(varName => {
    const value = env[varName as keyof typeof env]
    const status = value ? '✅' : '⚠️'
    console.log(`${status} ${varName}: ${value ? 'Set' : 'Not set'}`)
  })

  // Check feature flags
  console.log('\nFeature Flags:')
  console.log(`✅ NEXT_PUBLIC_ENABLE_ANALYTICS: ${env.NEXT_PUBLIC_ENABLE_ANALYTICS}`)
  console.log(`✅ NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE: ${env.NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE}`)

  console.log('\n✨ Environment verification complete!')
}

verifyEnvironment() 