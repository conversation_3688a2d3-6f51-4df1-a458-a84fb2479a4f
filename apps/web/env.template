# Application
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3001

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/myapp_dev
DATABASE_POOL_SIZE=5

# Authentication
NEXTAUTH_SECRET=development-secret-key-min-32-chars
NEXTAUTH_URL=http://localhost:3000

# OAuth (Development)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# External Services (Test Keys)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
SENDGRID_API_KEY=SG....

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE=false

# Redis (Optional)
REDIS_URL=redis://localhost:6379

# File Storage (Local/Development)
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin
AWS_REGION=us-east-1
AWS_S3_BUCKET=myapp-dev

# Monitoring (Optional)
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
VERCEL_ANALYTICS_ID=your-analytics-id 