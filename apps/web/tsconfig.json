{"extends": "@repo/typescript-config/nextjs.json", "compilerOptions": {"plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"]}, "incremental": true, "tsBuildInfoFile": ".tsbuildinfo", "skipLibCheck": true, "skipDefaultLibCheck": true}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", "next.config.js", ".next/types/**/*.ts"], "exclude": ["node_modules", "**/*.test.*", "**/*.spec.*", "coverage"]}