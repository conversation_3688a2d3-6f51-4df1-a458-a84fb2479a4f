'use client'

import { useEffect } from 'react'
import { trackWebVitals } from '@/lib/web-vitals'

export function PerformanceMonitor() {
  useEffect(() => {
    // Track web vitals
    trackWebVitals()

    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            console.warn('Long task detected:', entry)
          }
        }
      })
      observer.observe({ entryTypes: ['longtask'] })

      return () => observer.disconnect()
    }
  }, [])

  return null
} 