'use client'

import { useEffect } from 'react'
import { initSentry, setUserContext } from '@/lib/sentry'
import { useSession } from 'next-auth/react'

export function SentryProvider({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession()

  useEffect(() => {
    // Initialize Sentry
    initSentry()

    // Set user context if authenticated
    if (session?.user) {
      setUserContext({
        email: session.user.email || undefined,
        username: session.user.name || undefined,
      })
    }
  }, [session])

  return <>{children}</>
} 