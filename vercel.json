{"buildCommand": "turbo build --filter=web", "outputDirectory": "apps/web/.next", "installCommand": "pnpm install", "framework": "nextjs", "regions": ["iad1", "sfo1", "lhr1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}]}