{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local", "tsconfig.json"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "!**/*.test.*", "!**/*.spec.*", "!**/*.stories.*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_*"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}}, "remoteCache": {"signature": true}}